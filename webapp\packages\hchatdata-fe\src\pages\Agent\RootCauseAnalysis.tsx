import React, { useState, useRef } from 'react';
import { Card, Tabs, message, But<PERSON>, Modal, Select, Space, Tooltip } from 'antd';
import { DeleteOutlined, PlusOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import <PERSON><PERSON><PERSON>enderer, { CanvasRendererRef } from './components/CanvasRenderer';
import {
  getMetricsRelationshipList,
  getDimensionRelationshipList,
  getDimensionList,
  getMetricList,
  deleteDimensionNode,
  deleteMetricsNode,
  addDimensionRelationshipNode,
  addMetricRelationshipNode
} from '../../services/system';

// 定义组件Props类型
interface RootCauseAnalysisProps {
  agentId?: number;
}

// 定义数据项类型
interface RelationshipItem {
  datasetId: number;
  filed: string;
  id: number;
  pid: number;
  relationName: string;
  type: string;
}

// 定义维度列表项类型
interface DimensionItem {
  id: number;
  modelId: number;
  name: string;
  bizName: string;
  description: string;
  status: number;
  sensitiveLevel: number;
  type: string;
  createdAt: string;
  createdBy: string;
  updatedAt: string;
  updatedBy: string;
  semanticType: string;
  alias: string;
  defaultValues: string;
  dimValueMaps: string;
  typeParams: string;
  expr: string;
  dataType: string;
  isTag: number;
  ext: string;
}

// 定义指标列表项类型
interface MetricItem {
  id: number;
  modelId: number;
  name: string;
  bizName: string;
  description: string;
  status: number;
  sensitiveLevel: number;
  type: string;
  createdAt: string;
  createdBy: string;
  updatedAt: string;
  updatedBy: string;
  dataFormatType: string;
  dataFormat: string;
  alias: string;
  classifications: string;
  relateDimensions: string;
  typeParams: string;
  ext: string;
  defineType: string;
  isPublish: number;
  isTag: number;
}

const RootCauseAnalysis: React.FC<RootCauseAnalysisProps> = ({ agentId }) => {
  const [activeKey, setActiveKey] = useState('dimension');
  const [addNodeModalVisible, setAddNodeModalVisible] = useState(false);
  const [selectedNodeId, setSelectedNodeId] = useState<number | null>(null); // 用于第二种新增方式
  const [dimensionOptions, setDimensionOptions] = useState<DimensionItem[]>([]);
  const [metricOptions, setMetricOptions] = useState<MetricItem[]>([]);
  const [selectedOption, setSelectedOption] = useState<number | undefined>(undefined);
  const [loading, setLoading] = useState(false);
  const [selectedNode, setSelectedNode] = useState<any>(null); // 当前选中的节点
  const canvasRef = useRef<CanvasRendererRef>(null);

  // 获取真实数据
  const fetchCanvasData = async (type: 'dimension' | 'metric'): Promise<any[]> => {
    if (!agentId) {
      message.warning('缺少agentId参数');
      return [];
    }

    try {
      let response;
      if (type === 'dimension') {
        response = await getDimensionRelationshipList(agentId);
      } else {
        response = await getMetricsRelationshipList(agentId);
      }

      if (response.code === 200 && response.data) {
        return response.data;
      } else {
        message.error(`获取${type === 'dimension' ? '维度' : '指标'}关系失败`);
        return [];
      }
    } catch (error) {
      console.error(`获取${type === 'dimension' ? '维度' : '指标'}数据失败:`, error);
      message.error(`获取${type === 'dimension' ? '维度' : '指标'}数据失败`);
      return [];
    }
  };

  // 删除选中的节点
  const handleDeleteNode = () => {
    if (!selectedNode) {
      message.warning('请先选择要删除的节点');
      return;
    }

    Modal.confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除节点 "${selectedNode.label}" 吗？`,
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        await performDeleteNode();
      },
    });
  };

  // 执行删除操作
  const performDeleteNode = async () => {
    if (!selectedNode || !agentId) {
      return;
    }

    setLoading(true);
    try {
      const deleteData = {
        id: parseInt(selectedNode.id),
        pid: selectedNode.originalData?.pid || 0,
        filed: selectedNode.originalData?.filed || '',
        type: selectedNode.originalData?.type || 'component',
        relationName: selectedNode.label || '',
        datasetId: selectedNode.originalData?.datasetId || 0,
      };

      let response;
      if (activeKey === 'dimension') {
        response = await deleteDimensionNode(deleteData);
      } else {
        response = await deleteMetricsNode(deleteData);
      }

      // 检查响应状态
      if (response.code === 200) {
        message.success(`节点 "${selectedNode.label}" 删除成功`);

        // 清除选中状态
        setSelectedNode(null);

        // 刷新画布数据
        if (canvasRef.current && canvasRef.current.refreshData) {
          canvasRef.current.refreshData();
        }
      } else {
        message.error(response.msg || '删除节点失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除节点失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取下拉选项数据
  const fetchOptions = async (type: 'dimension' | 'metric') => {
    if (!agentId) {
      message.warning('缺少agentId参数');
      return;
    }

    try {
      let response;
      if (type === 'dimension') {
        response = await getDimensionList(agentId);
        if (response.code === 200 && response.data) {
          setDimensionOptions(response.data);
        }
      } else {
        response = await getMetricList(agentId);
        if (response.code === 200 && response.data) {
          setMetricOptions(response.data);
        }
      }
    } catch (error) {
      console.error('获取选项数据失败:', error);
      message.error('获取选项数据失败');
    }
  };

  // 处理新增根节点（第一种方式：页面按钮）
  const handleAddNode = async () => {
    await fetchOptions(activeKey as 'dimension' | 'metric');
    setSelectedNodeId(null); // 第一种方式pid为0
    setAddNodeModalVisible(true);
  };

  // 处理新增子节点（第二种方式：节点悬浮加号）
  const handleAddChildNode = async (parentNodeId: number) => {
    await fetchOptions(activeKey as 'dimension' | 'metric');
    setSelectedNodeId(parentNodeId); // 第二种方式pid为父节点id
    setAddNodeModalVisible(true);
  };

  // 确认添加节点
  const handleConfirmAddNode = async () => {
    if (!selectedOption || !agentId) {
      message.warning('请选择要添加的节点');
      return;
    }

    setLoading(true);
    try {
      const currentOptions = activeKey === 'dimension' ? dimensionOptions : metricOptions;
      const selectedItem = currentOptions.find(item => item.id === selectedOption);

      if (!selectedItem) {
        message.error('选中的节点不存在');
        return;
      }

      const nodeData = {
        id: selectedItem.id,
        pid: selectedNodeId || 0, // 第一种方式pid为0，第二种方式为父节点id
        filed: selectedItem.bizName || selectedItem.name,
        type: 'component',
        relationName: selectedItem.name,
        datasetId: selectedItem.modelId || 0
      };

      let response;
      if (activeKey === 'dimension') {
        response = await addDimensionRelationshipNode(nodeData);
      } else {
        response = await addMetricRelationshipNode(nodeData);
      }

      // 检查响应状态
      if (response.code === 200) {
        message.success(`${activeKey === 'dimension' ? '维度' : '指标'}节点添加成功`);

        // 刷新画布数据
        if (canvasRef.current && canvasRef.current.refreshData) {
          canvasRef.current.refreshData();
        }

        // 关闭弹窗并重置状态
        setAddNodeModalVisible(false);
        setSelectedOption(undefined);
        setSelectedNodeId(null);
      } else {
        // 显示服务器返回的错误信息
        message.error(response.msg || `${activeKey === 'dimension' ? '维度' : '指标'}节点添加失败`);
      }
    } catch (error) {
      console.error('添加节点失败:', error);
      message.error('添加节点失败');
    } finally {
      setLoading(false);
    }
  };

  const tabItems = [
    {
      key: 'dimension',
      label: '维度',
      children: (
        <CanvasRenderer
          ref={activeKey === 'dimension' ? canvasRef : null}
          dataType="dimension"
          fetchData={() => fetchCanvasData('dimension')}
          onAddChildNode={handleAddChildNode}
          selectedNodeId={selectedNode?.id}
        />
      ),
    },
    {
      key: 'metric',
      label: '指标',
      children: (
        <CanvasRenderer
          ref={activeKey === 'metric' ? canvasRef : null}
          dataType="metric"
          fetchData={() => fetchCanvasData('metric')}
          onAddChildNode={handleAddChildNode}
          selectedNodeId={selectedNode?.id}
        />
      ),
    },
  ];

  const handleNodeClick = (node: any) => {
    console.log('节点点击:', node);
    setSelectedNode(node);
  };

  const handleEdgeClick = (edge: any) => {
    console.log('边点击:', edge);
    // 这里可以添加边点击的处理逻辑，比如编辑关系等
  };

  return (
    <div style={{ padding: '16px' }}>
      <Card
        title="归因分析"
        extra={
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddNode}
              size="small"
            >
              新增根节点
            </Button>
            <Tooltip title={selectedNode ? `删除节点: ${selectedNode.label}` : '请先选择要删除的节点'}>
              <Button
                type="primary"
                danger
                icon={<DeleteOutlined />}
                onClick={handleDeleteNode}
                disabled={!selectedNode}
                loading={loading}
                size="small"
              >
                删除节点
              </Button>
            </Tooltip>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {activeKey === 'dimension' ? '维度关系图' : '指标关系图'}
            </div>
          </Space>
        }
      >
        <Tabs
          activeKey={activeKey}
          onChange={(key) => {
            setActiveKey(key);
            setSelectedNode(null); // 切换tab时清除选中状态
          }}
          items={tabItems.map(item => ({
            ...item,
            children: React.cloneElement(item.children as React.ReactElement, {
              onNodeClick: handleNodeClick,
              onEdgeClick: handleEdgeClick,
            }),
          }))}
          type="card"
          tabBarStyle={{ marginBottom: '16px' }}
        />
      </Card>

      {/* 新增节点弹窗 */}
      <Modal
        title={`新增${activeKey === 'dimension' ? '维度' : '指标'}节点`}
        open={addNodeModalVisible}
        onOk={handleConfirmAddNode}
        onCancel={() => {
          setAddNodeModalVisible(false);
          setSelectedOption(undefined);
          setSelectedNodeId(null);
        }}
        confirmLoading={loading}
        width="40%"
        styles={{
          body: { minHeight: '350px', padding: '24px' }
        }}
      >
        <div style={{ padding: '20px 0' }}>
          <div style={{ marginBottom: '16px' }}>
            请选择要添加的{activeKey === 'dimension' ? '维度' : '指标'}：
          </div>
          <Select
            style={{ width: '100%' }}
            placeholder={`请选择${activeKey === 'dimension' ? '维度' : '指标'}`}
            value={selectedOption}
            onChange={setSelectedOption}
            showSearch
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            listHeight={200}
            virtual={true}
            options={(activeKey === 'dimension' ? dimensionOptions : metricOptions).map(item => ({
              value: item.id,
              label: `${item.name} (${item.bizName})`,
            }))}
          />
          {selectedNodeId && (
            <div style={{ marginTop: '12px', fontSize: '12px', color: '#666' }}>
              将作为子节点添加到选中的父节点下
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default RootCauseAnalysis;
