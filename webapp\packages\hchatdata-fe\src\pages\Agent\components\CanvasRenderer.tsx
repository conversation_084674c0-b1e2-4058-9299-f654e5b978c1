import React, { useEffect, useRef, useState, useImperativeHandle, forwardRef } from 'react';
import { Spin, message, Empty } from 'antd';
import G6, { Graph, GraphData } from '@antv/g6';

interface RawDataItem {
  datasetId: number;
  filed: string;
  id: number;
  pid: number;
  relationName: string;
  type: 'component' | 'calculated';
}

interface CanvasRendererProps {
  dataType: 'dimension' | 'metric';
  fetchData: () => Promise<RawDataItem[]>;
  onNodeClick?: (node: any) => void;
  onEdgeClick?: (edge: any) => void;
  onAddChildNode?: (parentNodeId: number) => void;
  selectedNodeId?: string | null; // 当前选中的节点ID
}

export interface CanvasRendererRef {
  refreshData: () => void;
}

const CanvasRenderer = forwardRef<CanvasRendererRef, CanvasRendererProps>(({
  dataType,
  fetchData,
  onNodeClick,
  onEdgeClick,
  onAddChildNode,
  selectedNodeId,
}, ref) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const graphRef = useRef<Graph | null>(null);
  const [loading, setLoading] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isEmpty, setIsEmpty] = useState(false);
  const [hoveredNodeId, setHoveredNodeId] = useState<string | null>(null);

  // 将后端数据转换为G6格式
  const transformDataToG6Format = (rawData: RawDataItem[]): GraphData => {
    // 数据验证：确保所有节点的父节点都存在
    const nodeIds = new Set(rawData.map(item => item.id));
    const validatedData = rawData.filter(item => {
      if (item.pid === 0) return true; // 根节点
      if (!nodeIds.has(item.pid)) {
        console.warn(`节点 ${item.id}(${item.relationName}) 的父节点 ${item.pid} 不存在，已过滤`);
        return false;
      }
      return true;
    });

    const nodes = validatedData.map(item => ({
      id: item.id.toString(),
      label: item.relationName,
      type: 'component-node', // 统一使用矩形节点
      style: {
        fill: '#ffffff',
        stroke: '#d9d9d9',
        lineWidth: 2,
      },
      labelCfg: {
        style: {
          fill: '#000',
          fontSize: 12,
          fontWeight: 'bold' as const,
        },
      },
      // 保存原始数据用于后续扩展
      originalData: item,
    }));

    const edges = validatedData
      .filter(item => item.pid !== 0)
      .map(item => ({
        id: `edge-${item.pid}-${item.id}`,
        source: item.pid.toString(),
        target: item.id.toString(),
        style: {
          stroke: '#666',
          lineWidth: 2,
          endArrow: {
            path: G6.Arrow.triangle(8, 8, 0),
            fill: '#666',
          },
        },
      }));

    return { nodes, edges };
  };

  // 初始化G6图
  const initGraph = () => {
    if (!containerRef.current) return;

    // 清理之前的图实例
    if (graphRef.current) {
      graphRef.current.destroy();
    }

    const width = containerRef.current.offsetWidth;
    const height = 600;

    // 注册自定义节点类型
    G6.registerNode('component-node', {
      draw(cfg, group) {
        const rect = group!.addShape('rect', {
          attrs: {
            x: -60,
            y: -25,
            width: 120,
            height: 50,
            fill: cfg?.style?.fill || '#ffffff',
            stroke: cfg?.style?.stroke || '#d9d9d9',
            lineWidth: cfg?.style?.lineWidth || 2,
            radius: 6,
            cursor: 'pointer',
          },
          name: 'rect-shape',
        });

        if (cfg?.label) {
          // 处理长文本换行
          const text = cfg.label as string;
          const fontSize = 12;

          if (text.length > 8) {
            // 如果文本太长，分两行显示
            const firstLine = text.substring(0, 6);
            const secondLine = text.substring(6);

            group!.addShape('text', {
              attrs: {
                x: 0,
                y: -8,
                textBaseline: 'middle',
                textAlign: 'center',
                text: firstLine,
                fill: '#000',
                fontSize,
                fontWeight: 'bold',
                cursor: 'pointer',
              },
              name: 'text-shape-1',
            });

            group!.addShape('text', {
              attrs: {
                x: 0,
                y: 8,
                textBaseline: 'middle',
                textAlign: 'center',
                text: secondLine,
                fill: '#000',
                fontSize,
                fontWeight: 'bold',
                cursor: 'pointer',
              },
              name: 'text-shape-2',
            });
          } else {
            group!.addShape('text', {
              attrs: {
                textBaseline: 'middle',
                textAlign: 'center',
                text: cfg.label,
                fill: '#000',
                fontSize,
                fontWeight: 'bold',
                cursor: 'pointer',
              },
              name: 'text-shape',
            });
          }
        }

        return rect;
      },

      // 添加节点状态更新方法
      setState(name, value, item) {
        const group = item?.getContainer();
        if (!group) return;

        const rectShape = group.find(element => element.get('name') === 'rect-shape');
        if (!rectShape) return;

        // 显示或隐藏加号按钮的通用方法
        const showAddButton = () => {
          // 先检查是否已经存在加号按钮，避免重复添加
          const existingButton = group.find(element => element.get('name') === 'add-button-bg');
          if (existingButton) return;

          const addButton = group.addShape('circle', {
            attrs: {
              x: 0,
              y: 25, // 圆心在节点下边框上
              r: 12,
              fill: '#52c41a',
              stroke: '#389e0d',
              lineWidth: 2,
              cursor: 'pointer',
            },
            name: 'add-button-bg',
          });

          const addIcon = group.addShape('text', {
            attrs: {
              x: 0,
              y: 25,
              textBaseline: 'middle',
              textAlign: 'center',
              text: '+',
              fill: '#fff',
              fontSize: 16,
              fontWeight: 'bold',
              cursor: 'pointer',
            },
            name: 'add-button-icon',
          });

          // 绑定点击事件
          addButton.on('click', (evt: any) => {
            evt.stopPropagation();
            const nodeId = item?.getModel()?.id;
            if (nodeId && onAddChildNode) {
              onAddChildNode(parseInt(nodeId as string));
            }
          });

          addIcon.on('click', (evt: any) => {
            evt.stopPropagation();
            const nodeId = item?.getModel()?.id;
            if (nodeId && onAddChildNode) {
              onAddChildNode(parseInt(nodeId as string));
            }
          });
        };

        const hideAddButton = () => {
          const addButtonBg = group.find(element => element.get('name') === 'add-button-bg');
          const addButtonIcon = group.find(element => element.get('name') === 'add-button-icon');
          if (addButtonBg) {
            group.removeChild(addButtonBg);
          }
          if (addButtonIcon) {
            group.removeChild(addButtonIcon);
          }
        };

        if (name === 'hover') {
          if (value) {
            // 设置悬浮样式
            rectShape.attr({
              fill: '#69c0ff',
              stroke: '#1890ff',
              lineWidth: 2,
            });
            showAddButton();
          } else {
            // 检查是否被选中，如果没有被选中则恢复默认样式
            const isSelected = item?.hasState('selected');
            if (!isSelected) {
              rectShape.attr({
                fill: '#ffffff',
                stroke: '#d9d9d9',
                lineWidth: 2,
              });
              hideAddButton();
            }
          }
        }

        if (name === 'selected') {
          if (value) {
            // 设置选中样式
            rectShape.attr({
              fill: '#69c0ff',
              stroke: '#1890ff',
              lineWidth: 2,
            });
            showAddButton();
          } else {
            // 检查是否被悬浮，如果没有被悬浮则恢复默认样式
            const isHovered = item?.hasState('hover');
            if (!isHovered) {
              rectShape.attr({
                fill: '#ffffff',
                stroke: '#d9d9d9',
                lineWidth: 2,
              });
              hideAddButton();
            }
          }
        }
      },
    });



    const graph = new G6.Graph({
      container: containerRef.current,
      width,
      height,
      layout: {
        type: 'dagre',
        rankdir: 'TB',
        align: 'UL',
        nodesep: 50,
        ranksep: 80,
      },
      defaultNode: {
        type: 'component-node',
        size: [100, 40],
      },
      defaultEdge: {
        type: 'polyline',
        style: {
          radius: 10,
          offset: 15,
          endArrow: true,
          lineWidth: 2,
          stroke: '#666',
        },
      },
      modes: {
        default: [
          'drag-canvas',
          'zoom-canvas',
          'drag-node',
        ],
      },
      // 禁用默认的fitView，改为手动控制
      fitView: false,
      fitViewPadding: [20, 40, 50, 20],
      nodeStateStyles: {
        hover: {
          fill: '#69c0ff',
          stroke: '#1890ff',
          lineWidth: 2,
        },
        selected: {
          fill: '#69c0ff',
          stroke: '#1890ff',
          lineWidth: 2,
        },
      },
      edgeStateStyles: {
        hover: {
          stroke: '#389e0d',
          lineWidth: 3,
        },
      },
    });

    // 绑定事件
    graph.on('node:click', (evt) => {
      const { item } = evt;
      if (item) {
        // 清除所有节点的选中状态
        graph.getNodes().forEach(node => {
          graph.setItemState(node, 'selected', false);
        });

        // 设置当前节点为选中状态
        graph.setItemState(item, 'selected', true);

        if (onNodeClick) {
          onNodeClick(item.getModel());
        }
      }
    });

    graph.on('edge:click', (evt) => {
      const { item } = evt;
      if (item && onEdgeClick) {
        onEdgeClick(item.getModel());
      }
    });

    graph.on('node:mouseenter', (evt) => {
      const { item } = evt;
      if (item) {
        graph.setItemState(item, 'hover', true);
        // 设置鼠标为手型
        graph.get('canvas').get('el').style.cursor = 'pointer';
      }
    });

    graph.on('node:mouseleave', (evt) => {
      const { item } = evt;
      if (item) {
        graph.setItemState(item, 'hover', false);
        // 恢复默认鼠标样式
        graph.get('canvas').get('el').style.cursor = 'default';
      }
    });

    graph.on('edge:mouseenter', (evt) => {
      const { item } = evt;
      if (item) {
        graph.setItemState(item, 'hover', true);
        // 设置鼠标为手型
        graph.get('canvas').get('el').style.cursor = 'pointer';
      }
    });

    graph.on('edge:mouseleave', (evt) => {
      const { item } = evt;
      if (item) {
        graph.setItemState(item, 'hover', false);
        // 恢复默认鼠标样式
        graph.get('canvas').get('el').style.cursor = 'default';
      }
    });

    graphRef.current = graph;
    return graph;
  };

  // 刷新数据方法（供外部调用）
  const refreshData = () => {
    loadData();
  };

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    refreshData,
  }));

  // 加载数据
  const loadData = async () => {
    setLoading(true);
    setIsEmpty(false);
    try {
      const rawData = await fetchData();
      const g6Data = transformDataToG6Format(rawData);

      // 检查数据是否为空
      const hasData = g6Data.nodes && g6Data.nodes.length > 0;
      setIsEmpty(!hasData);

      if (graphRef.current) {
        if (hasData) {
          graphRef.current.data(g6Data);
          graphRef.current.render();

          // 根据节点数量调整缩放策略
          const nodeCount = g6Data.nodes?.length || 0;
          console.log('🔍 [CanvasRenderer] 节点数量:', nodeCount);

          if (nodeCount <= 3) {
            console.log('📍 [CanvasRenderer] 少量节点模式 - 定位到左上角');
            // 少量节点时，使用固定缩放并定位到左上角
            graphRef.current.zoomTo(1);

            // 获取节点的边界框
            const bbox = graphRef.current.getGroup().getCanvasBBox();
            console.log('📦 [CanvasRenderer] 节点边界框:', bbox);

            // 计算需要移动的距离，使内容距离左上角100px
            const targetX = 100 - bbox.minX;
            const targetY = 100 - bbox.minY;
            console.log('🎯 [CanvasRenderer] 目标移动距离:', { targetX, targetY });

            // 移动画布内容到指定位置
            graphRef.current.translate(targetX, targetY);
          } else {
            console.log('🔄 [CanvasRenderer] 多节点模式 - 重新初始化画布');

            // 完全重新初始化画布来避免累积的变换问题
            const currentContainer = graphRef.current.getContainer();
            const currentWidth = graphRef.current.getWidth();
            const currentHeight = graphRef.current.getHeight();

            // 销毁当前图实例
            graphRef.current.destroy();

            // 重新创建图实例
            const newGraph = new G6.Graph({
              container: currentContainer,
              width: currentWidth,
              height: currentHeight,
              layout: {
                type: 'dagre',
                rankdir: 'TB',
                align: 'UL',
                nodesep: 50,
                ranksep: 80,
              },
              defaultNode: {
                type: 'component-node',
                size: [100, 40],
              },
              defaultEdge: {
                type: 'polyline',
                style: {
                  radius: 10,
                  offset: 15,
                  endArrow: true,
                  lineWidth: 2,
                  stroke: '#666',
                },
              },
              modes: {
                default: [
                  'drag-canvas',
                  'zoom-canvas',
                  'drag-node',
                ],
              },
              fitView: false,
              fitViewPadding: [20, 40, 50, 20],
              nodeStateStyles: {
                hover: {
                  fill: '#69c0ff',
                  stroke: '#1890ff',
                  lineWidth: 2,
                },
                selected: {
                  fill: '#69c0ff',
                  stroke: '#1890ff',
                  lineWidth: 2,
                },
              },
              edgeStateStyles: {
                hover: {
                  stroke: '#389e0d',
                  lineWidth: 3,
                },
              },
            });

            // 重新绑定所有事件（复制原来的事件绑定逻辑）
            newGraph.on('node:click', (evt) => {
              const { item } = evt;
              if (item) {
                newGraph.getNodes().forEach(node => {
                  newGraph.setItemState(node, 'selected', false);
                });
                newGraph.setItemState(item, 'selected', true);
                if (onNodeClick) {
                  onNodeClick(item.getModel());
                }
              }
            });

            newGraph.on('edge:click', (evt) => {
              const { item } = evt;
              if (item && onEdgeClick) {
                onEdgeClick(item.getModel());
              }
            });

            newGraph.on('node:mouseenter', (evt) => {
              const { item } = evt;
              if (item) {
                newGraph.setItemState(item, 'hover', true);
                newGraph.get('canvas').get('el').style.cursor = 'pointer';
              }
            });

            newGraph.on('node:mouseleave', (evt) => {
              const { item } = evt;
              if (item) {
                newGraph.setItemState(item, 'hover', false);
                newGraph.get('canvas').get('el').style.cursor = 'default';
              }
            });

            newGraph.on('edge:mouseenter', (evt) => {
              const { item } = evt;
              if (item) {
                newGraph.setItemState(item, 'hover', true);
                newGraph.get('canvas').get('el').style.cursor = 'pointer';
              }
            });

            newGraph.on('edge:mouseleave', (evt) => {
              const { item } = evt;
              if (item) {
                newGraph.setItemState(item, 'hover', false);
                newGraph.get('canvas').get('el').style.cursor = 'default';
              }
            });

            // 更新图实例引用
            graphRef.current = newGraph;

            // 设置数据并渲染
            newGraph.data(g6Data);
            newGraph.render();

            // 使用fitView
            newGraph.fitView([20, 40, 50, 20], {
              onlyOutOfViewPort: false,
              direction: 'both'
            });

            console.log('✅ [CanvasRenderer] 画布重新初始化完成');
          }
        } else {
          // 清空画布
          graphRef.current.data({ nodes: [], edges: [] });
          graphRef.current.render();
        }
      }
    } catch (error) {
      message.error('加载数据失败');
      console.error('Failed to load canvas data:', error);
      setIsEmpty(true);
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时初始化图实例（只初始化一次）
  useEffect(() => {
    if (!isInitialized) {
      const graph = initGraph();
      if (graph) {
        setIsInitialized(true);
      }
    }

    // 清理函数
    return () => {
      if (graphRef.current) {
        graphRef.current.destroy();
        graphRef.current = null;
        setIsInitialized(false);
      }
    };
  }, []);

  // 当dataType变化时，只重新加载数据，不重新初始化图实例
  useEffect(() => {
    if (isInitialized && graphRef.current) {
      loadData();
    }
  }, [dataType, isInitialized]);

  // 窗口大小变化时重新调整
  useEffect(() => {
    const handleResize = () => {
      if (graphRef.current && containerRef.current) {
        const width = containerRef.current.offsetWidth;
        graphRef.current.changeSize(width, 600);

        // 根据当前节点数量调整缩放策略
        const nodes = graphRef.current.getNodes();
        const nodeCount = nodes?.length || 0;

        if (nodeCount > 0) {
          // 先使用fitView确保所有节点都在可视范围内
          graphRef.current.fitView([20, 40, 50, 20], {
            onlyOutOfViewPort: false,
            direction: 'both'
          });

          // 如果节点数量较少，再调整到左上角位置
          if (nodeCount <= 3) {
            setTimeout(() => {
              if (graphRef.current) {
                // 重置到1:1缩放
                graphRef.current.zoomTo(1);

                // 获取节点的边界框
                const bbox = graphRef.current.getGroup().getCanvasBBox();

                // 计算当前画布的变换
                const matrix = graphRef.current.getGroup().getMatrix();
                const currentTranslateX = matrix ? matrix[6] : 0;
                const currentTranslateY = matrix ? matrix[7] : 0;

                // 计算需要移动的距离，使内容距离左上角100px
                const targetX = 100 - bbox.minX + currentTranslateX;
                const targetY = 100 - bbox.minY + currentTranslateY;

                // 移动画布内容到指定位置
                graphRef.current.translate(targetX - currentTranslateX, targetY - currentTranslateY);
              }
            }, 100);
          }
        }
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div style={{ position: 'relative', width: '100%', height: '600px' }}>
      <Spin spinning={loading}>
        <div
          ref={containerRef}
          style={{
            width: '100%',
            height: '600px',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            backgroundColor: '#fafafa',
            position: 'relative',
          }}
        >
          {/* 空数据提示 */}
          {isEmpty && !loading && (
            <div
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                textAlign: 'center',
                zIndex: 10,
              }}
            >
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={
                  <span style={{ color: '#999', fontSize: '14px' }}>
                    暂无{dataType === 'dimension' ? '维度' : '指标'}关系数据
                    <br />
                    <span style={{ fontSize: '12px', color: '#ccc' }}>
                      请检查当前Agent是否配置了相关数据
                    </span>
                  </span>
                }
              />
            </div>
          )}
        </div>
      </Spin>
    </div>
  );
});

export default CanvasRenderer;
