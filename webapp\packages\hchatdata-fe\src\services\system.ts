import request from 'umi-request';

export function testLLMConn(data: any) {
  return request(`${process.env.CHAT_API_BASE_URL}model/testConnection`, {
    method: 'POST',
    data,
  });
}

export function getLlmModelTypeList(): Promise<any> {
  return request(`${process.env.CHAT_API_BASE_URL}model/getModelTypeList`, {
    method: 'GET',
  });
}

export function getLlmModelAppList(): Promise<any> {
  return request(`${process.env.CHAT_API_BASE_URL}model/getModelAppList`, {
    method: 'GET',
  });
}

export function getLlmList(): Promise<any> {
  return request(`${process.env.CHAT_API_BASE_URL}model/getModelList`, {
    method: 'GET',
  });
}

export function getLlmConfig(): Promise<any> {
  return request(`${process.env.CHAT_API_BASE_URL}model/getModelParameters`, {
    method: 'GET',
  });
}

export function getMetricsRelationshipList(agentId: number): Promise<any> {
  return request(`${process.env.RELATIONSHIP_API_BASE_URL}getMetricsRelationshipList?agentId=${agentId}`, {
    method: 'GET',
  });
}

export function getDimensionRelationshipList(agentId: number): Promise<any> {
  return request(`${process.env.RELATIONSHIP_API_BASE_URL}getDimensionRelationshipList?agentId=${agentId}`, {
    method: 'GET',
  });
}
// 血缘关系节点参数类型定义
interface RelationshipNodeParams {
  id: number;
  pid: number;
  filed: string;
  type: string;
  relationName: string;
  datasetId: number;
}

// 获取维度列表
export function getDimensionList(agentId: number): Promise<any> {
  return request(`${process.env.RELATIONSHIP_API_BASE_URL}getDimensionList?agentId=${agentId}`, {
    method: 'GET',
  });
}

// 获取指标列表
export function getMetricList(agentId: number): Promise<any> {
  return request(`${process.env.RELATIONSHIP_API_BASE_URL}getMetricList?agentId=${agentId}`, {
    method: 'GET',
  });
}

// 删除维度节点
export function deleteDimensionNode(data: RelationshipNodeParams): Promise<any> {
  return request(`${process.env.RELATIONSHIP_API_BASE_URL}deleteDimensionRelationshipNode`, {
    method: 'POST',
    data,
  });
}

// 删除指标节点
export function deleteMetricsNode(data: RelationshipNodeParams): Promise<any> {
  return request(`${process.env.RELATIONSHIP_API_BASE_URL}deleteMetricsRelationshipNode`, {
    method: 'POST',
    data,
  });
}

// 保存指标血缘关系（节点）
export function addMetricRelationshipNode(data: RelationshipNodeParams): Promise<any> {
  return request(`${process.env.RELATIONSHIP_API_BASE_URL}addMetricRelationshipNode`, {
    method: 'POST',
    data,
  });
}

// 保存维度血缘关系（节点）
export function addDimensionRelationshipNode(data: RelationshipNodeParams): Promise<any> {
  return request(`${process.env.RELATIONSHIP_API_BASE_URL}addDimensionRelationshipNode`, {
    method: 'POST',
    data,
  });
}